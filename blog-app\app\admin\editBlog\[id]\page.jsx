'use client'
import { assets } from '@/Assets/assets'
import axios from 'axios'
import Image from 'next/image'
import { useRouter, useParams } from 'next/navigation'
import React, { useState, useEffect } from 'react'
import { toast } from 'react-toastify'

const EditBlogPage = () => {
    const router = useRouter();
    const params = useParams();
    const [loading, setLoading] = useState(true);
    const [image, setImage] = useState(null);
    const [currentImage, setCurrentImage] = useState('');
    const [categories, setCategories] = useState([]);
    const [authors, setAuthors] = useState([]);
    const [likesCount, setLikesCount] = useState(0);
    const [analytics, setAnalytics] = useState({
        totalViews: 0,
        uniqueVisitors: 0
    });
    const [data, setData] = useState({
        title: "",
        description: "",
        category: "",
        author: "",
        authorId: "",
        authorImg: "/author_img.png",
        commentsEnabled: true
    });
    const [showBlogSelector, setShowBlogSelector] = useState(false);
    const [allBlogs, setAllBlogs] = useState([]);
    const [searchTerm, setSearchTerm] = useState('');

    // Image insertion states
    const [showImageSelector, setShowImageSelector] = useState(false);
    const [allImages, setAllImages] = useState([]);
    const [imageSearchTerm, setImageSearchTerm] = useState('');
    const [imageUploadLoading, setImageUploadLoading] = useState(false);
    const [selectedImageFile, setSelectedImageFile] = useState(null);

    // Fetch categories function
    const fetchCategories = async () => {
        try {
            const response = await axios.get('/api/categories');
            if (response.data.success && response.data.categories.length > 0) {
                setCategories(response.data.categories);
            } else {
                console.error("No categories found");
                toast.error("Failed to load categories");
            }
        } catch (error) {
            console.error("Error fetching categories:", error);
            toast.error("Failed to load categories");
        }
    };

    // Fetch authors function
    const fetchAuthors = async () => {
        try {
            const response = await axios.get('/api/authors');
            if (response.data.success && response.data.authors.length > 0) {
                setAuthors(response.data.authors);
            } else {
                console.error("No authors found");
                toast.error("Failed to load authors");
            }
        } catch (error) {
            console.error("Error fetching authors:", error);
            toast.error("Failed to load authors");
        }
    };

    // Fetch likes count
    const fetchLikesCount = async () => {
        try {
            const response = await axios.get(`/api/blog/likes?id=${params.id}`);
            if (response.data.success) {
                setLikesCount(response.data.count);
            }
        } catch (error) {
            console.error("Error fetching likes count:", error);
            // Don't show error toast for this as it's not critical
        }
    };

    // Fetch blog analytics
    const fetchBlogAnalytics = async () => {
        try {
            const response = await axios.get(`/api/blog/analytics?id=${params.id}`);
            if (response.data.success) {
                setAnalytics(response.data.analytics);
            }
        } catch (error) {
            console.error("Error fetching blog analytics:", error);
            // Don't show error toast for this as it's not critical
        }
    };

    // Fetch all blogs for the selector
    const fetchAllBlogs = async () => {
        try {
            const response = await axios.get('/api/blog');
            setAllBlogs(response.data.blogs || []);
        } catch (error) {
            console.error("Error fetching blogs:", error);
        }
    };

    // Insert a blog mention at the cursor position
    const insertBlogMention = (blogId, blogTitle) => {
        // Create the mention format: [[blogId|blogTitle]]
        const mention = `[[${blogId}|${blogTitle}]]`;

        // Get the textarea element
        const textarea = document.getElementById('blog-description');
        const cursorPos = textarea.selectionStart;

        // Insert the mention at cursor position
        const textBefore = data.description.substring(0, cursorPos);
        const textAfter = data.description.substring(cursorPos);

        setData({
            ...data,
            description: textBefore + mention + textAfter
        });

        // Close the selector
        setShowBlogSelector(false);

        // Focus back on textarea
        setTimeout(() => {
            textarea.focus();
            textarea.setSelectionRange(cursorPos + mention.length, cursorPos + mention.length);
        }, 100);
    };

    // Image-related functions
    const fetchAllImages = async () => {
        try {
            const response = await axios.get('/api/images', {
                params: { blogId: params.id, limit: 50 }
            });
            if (response.data.success) {
                setAllImages(response.data.images);
            }
        } catch (error) {
            console.error("Error fetching images:", error);
            toast.error("Failed to fetch images");
        }
    };

    const handleImageUpload = async () => {
        if (!selectedImageFile) {
            toast.error("Please select an image file");
            return;
        }

        setImageUploadLoading(true);
        try {
            const formData = new FormData();
            formData.append('image', selectedImageFile);
            formData.append('blogId', params.id);

            const response = await axios.post('/api/upload/image', formData);

            if (response.data.success) {
                toast.success("Image uploaded successfully");
                setSelectedImageFile(null);
                // Refresh the images list
                await fetchAllImages();
            } else {
                toast.error(response.data.message || "Failed to upload image");
            }
        } catch (error) {
            console.error("Error uploading image:", error);
            toast.error("Failed to upload image");
        } finally {
            setImageUploadLoading(false);
        }
    };

    const deleteImage = async (imageId, imageUrl) => {
        if (!window.confirm("Are you sure you want to delete this image? This action cannot be undone.")) {
            return;
        }

        try {
            const response = await axios.delete(`/api/images/${imageId}`);
            if (response.data.success) {
                toast.success("Image deleted successfully");
                // Refresh the images list
                await fetchAllImages();
            } else {
                toast.error(response.data.message || "Failed to delete image");
            }
        } catch (error) {
            console.error("Error deleting image:", error);
            toast.error("Failed to delete image");
        }
    };

    const insertImageReference = (imageUrl, filename) => {
        const imageRef = `{{image:${imageUrl}|${filename}}}`;
        const textarea = document.getElementById('blog-description');
        const cursorPos = textarea.selectionStart;
        const textBefore = data.description.substring(0, cursorPos);
        const textAfter = data.description.substring(cursorPos);

        setData({
            ...data,
            description: textBefore + imageRef + textAfter
        });

        setShowImageSelector(false);

        setTimeout(() => {
            textarea.focus();
            textarea.setSelectionRange(cursorPos + imageRef.length, cursorPos + imageRef.length);
        }, 100);
    };

    // Fetch the blog data
    useEffect(() => {
        const fetchBlog = async () => {
            try {
                const response = await axios.get('/api/blog', {
                    params: {
                        id: params.id
                    }
                });
                
                const blog = response.data;
                setData({
                    title: blog.title || "",
                    description: blog.description || "",
                    category: blog.category || "Startup",
                    author: blog.author || "",
                    authorId: blog.authorId || "",
                    authorImg: blog.authorImg || "/author_img.png",
                    commentsEnabled: blog.commentsEnabled !== undefined ? blog.commentsEnabled : true
                });
                setCurrentImage(blog.image || "");
                
                // Fetch categories and authors
                await fetchCategories();
                await fetchAuthors();
                await fetchLikesCount();
                await fetchBlogAnalytics();
                
                setLoading(false);
            } catch (error) {
                console.error("Error fetching blog:", error);
                toast.error("Failed to load blog data");
                router.push('/admin/blogList');
            }
        };
        
        fetchBlog();
    }, [params.id, router]);

    const onChangeHandler = (event) => {
        const name = event.target.name;
        const value = event.target.value;
        
        if (name === 'authorId') {
            // Find the selected author
            const selectedAuthor = authors.find(author => author._id === value);
            if (selectedAuthor) {
                setData(data => ({ 
                    ...data, 
                    author: selectedAuthor.name,
                    authorId: selectedAuthor._id,
                    authorImg: selectedAuthor.image || "/author_img.png"
                }));
            }
        } else {
            setData(data => ({ ...data, [name]: value }));
        }
    };

    const onSubmitHandler = async (e) => {
        e.preventDefault();
        try {
            const formData = new FormData();
            formData.append('id', params.id);
            formData.append('title', data.title);
            formData.append('description', data.description);
            formData.append('category', data.category);
            formData.append('author', data.author);
            formData.append('authorId', data.authorId);
            formData.append('authorImg', data.authorImg);
            formData.append('commentsEnabled', data.commentsEnabled);

            // Only append image if a new one was selected
            if (image) {
                formData.append('image', image);
            }

            const response = await axios.put('/api/blog', formData);

            if (response.data.success) {
                toast.success(response.data.msg || "Blog updated successfully");
                // Stay on the same edit page after successful update
                // Reset the image state if a new image was uploaded
                if (image) {
                    setImage(null);
                }
            } else {
                toast.error(response.data.msg || "Error updating blog");
            }
        } catch (error) {
            console.error("Error updating blog:", error);
            toast.error("Failed to update blog");
        }
    };

    const deleteBlog = async () => {
        if (!window.confirm("Are you sure you want to delete this blog? This action cannot be undone.")) {
            return;
        }

        try {
            const response = await axios.delete('/api/blog', {
                params: {
                    id: params.id
                }
            });

            if (response.data.success) {
                toast.success(response.data.msg || "Blog deleted successfully");
                router.push('/admin/blogList');
            } else {
                toast.error(response.data.msg || "Error deleting blog");
            }
        } catch (error) {
            console.error("Error deleting blog:", error);
            toast.error("Failed to delete blog");
        }
    };

    if (loading) {
        return (
            <div className='pt-5 px-5 sm:pt-12 sm:pl-16'>
                <p>Loading blog data...</p>
            </div>
        );
    }

    return (
        <>
            <form onSubmit={onSubmitHandler} className='pt-5 px-5 sm:pt-12 sm:pl-16'>
                <div className="flex justify-between items-center mb-6">
                    <h1 className='text-2xl font-bold'>Edit Blog</h1>
                    
                    <div className="flex items-center gap-4">
                        <div className="flex items-center gap-2 bg-gray-100 px-3 py-2 rounded-md">
                            <svg 
                                xmlns="http://www.w3.org/2000/svg" 
                                width="20" 
                                height="20" 
                                viewBox="0 0 24 24" 
                                fill="currentColor"
                                className="text-red-500"
                            >
                                <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z" />
                            </svg>
                            <span className="font-medium">{likesCount}</span>
                            <span className="text-sm text-gray-500">likes</span>
                        </div>
                        
                        <div className="flex items-center gap-2 bg-gray-100 px-3 py-2 rounded-md">
                            <svg 
                                xmlns="http://www.w3.org/2000/svg" 
                                width="20" 
                                height="20" 
                                viewBox="0 0 24 24" 
                                fill="none"
                                stroke="currentColor"
                                strokeWidth="2"
                                className="text-blue-500"
                            >
                                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                                <circle cx="12" cy="12" r="3"></circle>
                            </svg>
                            <span className="font-medium">{analytics.totalViews}</span>
                            <span className="text-sm text-gray-500">views</span>
                        </div>
                    </div>
                </div>
                
                <p className='text-xl'>Current thumbnail</p>
                <div className='mt-4 mb-4'>
                    <Image 
                        src={currentImage} 
                        width={200} 
                        height={120} 
                        alt='Current thumbnail'
                        className='border border-gray-300'
                    />
                </div>
                
                <p className='text-xl'>Upload new thumbnail (optional)</p>
                <label htmlFor="image">
                    <Image 
                        className='mt-4 cursor-pointer' 
                        src={!image ? assets.upload_area : URL.createObjectURL(image)} 
                        width={140} 
                        height={70} 
                        alt=''
                    />
                </label>
                <input 
                    onChange={(e) => setImage(e.target.files[0])} 
                    type="file" 
                    id='image' 
                    hidden 
                />
                
                <p className='text-xl mt-4'>Blog title</p>
                <input 
                    name='title' 
                    onChange={onChangeHandler} 
                    value={data.title} 
                    className='w-full sm:w-[500px] mt-4 px-4 py-3 border' 
                    type="text" 
                    placeholder='Type here' 
                    required 
                />
                
                <div className='mt-4'>
                    <p className='text-xl'>Blog Description</p>
                    <div className="flex items-start mt-4">
                        <textarea 
                            id="blog-description"
                            name='description' 
                            onChange={onChangeHandler} 
                            value={data.description} 
                            className='w-full sm:w-[500px] px-4 py-3 border' 
                            placeholder='Write content here' 
                            rows={6} 
                            required 
                        />
                    </div>
                    <div className="mt-2 flex items-center flex-wrap gap-4">
                        <button
                            type="button"
                            onClick={() => {
                                fetchAllBlogs();
                                setShowBlogSelector(true);
                            }}
                            className="text-sm flex items-center text-blue-600 hover:text-blue-800"
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101" />
                            </svg>
                            Mention another blog
                        </button>

                        <button
                            type="button"
                            onClick={() => {
                                fetchAllImages();
                                setShowImageSelector(true);
                            }}
                            className="text-sm flex items-center text-green-600 hover:text-green-800"
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                            Insert image
                        </button>

                        <div className="text-xs text-gray-500">
                            <span>Formats: [[blogId|blogTitle]] | {`{{image:url|filename}}`}</span>
                        </div>
                    </div>
                    
                    {/* Blog selector modal */}
                    {showBlogSelector && (
                        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                            <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[80vh] overflow-auto">
                                <div className="flex justify-between items-center mb-4">
                                    <h3 className="text-lg font-semibold">Select a blog to mention</h3>
                                    <button 
                                        onClick={() => setShowBlogSelector(false)}
                                        className="text-gray-500 hover:text-gray-700"
                                    >
                                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                    </button>
                                </div>
                                
                                <input
                                    type="text"
                                    placeholder="Search blogs..."
                                    className="w-full px-4 py-2 border rounded-md mb-4"
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                />
                                
                                <div className="divide-y">
                                    {allBlogs
                                        .filter(blog => blog.title.toLowerCase().includes(searchTerm.toLowerCase()))
                                        .map(blog => (
                                            <div 
                                                key={blog._id} 
                                                className="py-3 px-2 hover:bg-gray-100 cursor-pointer flex items-center"
                                                onClick={() => insertBlogMention(blog._id, blog.title)}
                                            >
                                                <div className="w-12 h-12 relative mr-3">
                                                    <Image 
                                                        src={blog.image} 
                                                        alt={blog.title}
                                                        fill
                                                        className="object-cover rounded"
                                                    />
                                                </div>
                                                <div>
                                                    <h4 className="font-medium">{blog.title}</h4>
                                                    <p className="text-sm text-gray-500">{blog.category}</p>
                                                </div>
                                            </div>
                                        ))}
                                </div>
                            </div>
                        </div>
                    )}

                    {/* Image selector modal */}
                    {showImageSelector && (
                        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                            <div className="bg-white rounded-lg p-6 w-full max-w-4xl max-h-[80vh] overflow-auto">
                                <div className="flex justify-between items-center mb-4">
                                    <h3 className="text-lg font-semibold">Insert Image</h3>
                                    <button
                                        onClick={() => setShowImageSelector(false)}
                                        className="text-gray-500 hover:text-gray-700"
                                    >
                                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                    </button>
                                </div>

                                {/* Upload new image section */}
                                <div className="mb-6 p-4 border rounded-lg bg-gray-50">
                                    <h4 className="font-medium mb-3">Upload New Image</h4>
                                    <div className="flex items-center gap-4">
                                        <input
                                            type="file"
                                            accept="image/*"
                                            onChange={(e) => setSelectedImageFile(e.target.files[0])}
                                            className="flex-1"
                                        />
                                        <button
                                            type="button"
                                            onClick={handleImageUpload}
                                            disabled={!selectedImageFile || imageUploadLoading}
                                            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
                                        >
                                            {imageUploadLoading ? 'Uploading...' : 'Upload'}
                                        </button>
                                    </div>
                                </div>

                                {/* Search existing images */}
                                <input
                                    type="text"
                                    placeholder="Search images..."
                                    className="w-full px-4 py-2 border rounded-md mb-4"
                                    value={imageSearchTerm}
                                    onChange={(e) => setImageSearchTerm(e.target.value)}
                                />

                                {/* Images grid */}
                                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                                    {allImages
                                        .filter(image => image.filename.toLowerCase().includes(imageSearchTerm.toLowerCase()))
                                        .map(image => (
                                            <div
                                                key={image._id}
                                                className="border rounded-lg p-2 hover:bg-gray-100 cursor-pointer relative group"
                                            >
                                                <div
                                                    className="aspect-square relative mb-2"
                                                    onClick={() => insertImageReference(image.url, image.filename)}
                                                >
                                                    <Image
                                                        src={image.url}
                                                        alt={image.filename}
                                                        fill
                                                        className="object-cover rounded"
                                                    />
                                                    {/* Delete button */}
                                                    <button
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            deleteImage(image._id, image.url);
                                                        }}
                                                        className="absolute top-1 right-1 bg-red-500 hover:bg-red-600 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                                                        title="Delete image"
                                                    >
                                                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                                        </svg>
                                                    </button>
                                                </div>
                                                <div onClick={() => insertImageReference(image.url, image.filename)}>
                                                    <p className="text-xs text-gray-600 truncate">{image.filename}</p>
                                                    <p className="text-xs text-gray-400">{new Date(image.uploadDate).toLocaleDateString()}</p>
                                                </div>
                                            </div>
                                        ))}
                                </div>

                                {allImages.length === 0 && (
                                    <div className="text-center py-8 text-gray-500">
                                        No images found. Upload your first image above.
                                    </div>
                                )}
                            </div>
                        </div>
                    )}
                </div>

                <p className='text-xl mt-4'>Blog category</p>
                <select 
                    name="category" 
                    onChange={onChangeHandler} 
                    value={data.category} 
                    className='w-40 mt-4 px-4 py-3 border text-gray-500'
                >
                    {categories.map(category => (
                        <option key={category._id} value={category.name}>
                            {category.name}
                        </option>
                    ))}
                </select>
                
                <p className='text-xl mt-4'>Blog author</p>
                {authors.length > 0 ? (
                    <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 mt-4">
                        <select 
                            name="authorId" 
                            onChange={onChangeHandler} 
                            value={data.authorId} 
                            className='w-full sm:w-40 px-4 py-3 border text-gray-500'
                        >
                            <option value="">Select an author</option>
                            {authors.map(author => (
                                <option key={author._id} value={author._id}>
                                    {author.name}
                                </option>
                            ))}
                        </select>
                        
                        {data.authorId && (
                            <div className="flex items-center gap-3 mt-2 sm:mt-0">
                                <img 
                                    src={data.authorImg} 
                                    alt={data.author} 
                                    className="w-10 h-10 rounded-full object-cover border border-gray-200" 
                                />
                                <span className="text-sm font-medium">{data.author}</span>
                            </div>
                        )}
                    </div>
                ) : (
                    <div className="mt-4">
                        <p className="text-red-500">No authors available. Please add authors in Settings.</p>
                        <input 
                            name='author' 
                            onChange={onChangeHandler} 
                            value={data.author} 
                            className='w-full sm:w-[500px] mt-2 px-4 py-3 border' 
                            type="text" 
                            placeholder='Author name' 
                            required 
                        />
                    </div>
                )}

                <div className='mt-6'>
                    <label className='flex items-center gap-3 cursor-pointer'>
                        <input
                            type="checkbox"
                            name="commentsEnabled"
                            checked={data.commentsEnabled}
                            onChange={(e) => setData(prev => ({...prev, commentsEnabled: e.target.checked}))}
                            className='w-4 h-4 text-black border-gray-300 rounded focus:ring-black'
                        />
                        <span className='text-sm font-medium text-gray-700'>
                            Enable comments for this blog post
                        </span>
                    </label>
                    <p className='text-xs text-gray-500 mt-1 ml-7'>
                        When enabled, readers can comment on this blog post
                    </p>
                </div>

                <div className='flex gap-4'>
                    <button
                        type="submit"
                        className='mt-8 w-40 h-12 bg-black text-white'
                    >
                        UPDATE
                    </button>

                    <button
                        type="button"
                        onClick={() => router.push('/admin/blogList')}
                        className='mt-8 w-40 h-12 border border-black'
                    >
                        CANCEL
                    </button>

                    <button
                        type="button"
                        onClick={deleteBlog}
                        className='mt-8 w-40 h-12 bg-red-600 text-white hover:bg-red-700'
                    >
                        DELETE
                    </button>
                </div>
            </form>
        </>
    );
};

export default EditBlogPage;




