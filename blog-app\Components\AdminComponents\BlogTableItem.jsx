import { assets } from '@/Assets/assets'
import Image from 'next/image'
import Link from 'next/link'
import React from 'react'

const BlogTableItem = ({number, authorImg, title, author, date, deleteBlog, mongoId, commentsEnabled}) => {
    const BlogDate = new Date(date);
    
    return (
        <tr className='bg-white border-b hover:bg-gray-50'>
            <td className='px-6 py-4 font-medium text-gray-900 text-center'>
                {number}
            </td>
            <td className='px-6 py-4 truncate'>
                {title || "No title"}
            </td>
            <th scope='row' className='hidden sm:table-cell px-6 py-4 font-medium text-gray-900'>
                <div className='flex items-center gap-3'>
                    <Image
                        width={40}
                        height={40}
                        src={authorImg ? authorImg : assets.profile_icon}
                        alt={author || "Author"}
                        className="rounded-full object-cover w-10 h-10 border border-gray-200"
                    />
                    <p className="truncate">{author || "No author"}</p>
                </div>
            </th>
            <td className='px-6 py-4 whitespace-nowrap'>
                {BlogDate.toDateString()}
            </td>
            <td className='px-6 py-4 whitespace-nowrap'>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    commentsEnabled
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                }`}>
                    {commentsEnabled ? 'Enabled' : 'Disabled'}
                </span>
            </td>
            <td className='px-6 py-4 whitespace-nowrap'>
                <div className='flex gap-3'>
                    <Link
                        href={`/blogs/${mongoId}`}
                        target="_blank"
                        className='cursor-pointer text-green-600 hover:underline'
                    >
                        View
                    </Link>
                    <Link
                        href={`/admin/editBlog/${mongoId}`}
                        className='cursor-pointer text-blue-600 hover:underline'
                    >
                        Edit
                    </Link>
                    <button
                        onClick={() => deleteBlog(mongoId)}
                        className='cursor-pointer text-red-600 hover:underline'
                    >
                        Delete
                    </button>
                </div>
            </td>
        </tr>
    )
}

export default BlogTableItem
