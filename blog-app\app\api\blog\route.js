import { ConnectDB } from "@/lib/config/db"
import BlogModel from "@/lib/models/BlogModel";
import ImageModel from "@/lib/models/ImageModel";
const { NextResponse } = require("next/server")
import { writeFile } from 'fs/promises'
const fs = require('fs')

const LoadDB = async () => {
  await ConnectDB();
}

LoadDB();


// API Endpoint to get all blogs
export async function GET(request) {
  try {
    const blogId = request.nextUrl.searchParams.get("id");
    if (blogId) {
      const blog = await BlogModel.findById(blogId);
      return NextResponse.json(blog);
    }
    else {
      // Get pagination parameters
      const page = parseInt(request.nextUrl.searchParams.get("page")) || 1;
      const limit = parseInt(request.nextUrl.searchParams.get("limit")) || 10;
      const skip = (page - 1) * limit;

      // Get total count for pagination
      const totalBlogs = await BlogModel.countDocuments({});
      const totalPages = Math.ceil(totalBlogs / limit);

      // Fetch blogs with pagination and sort by date (newest first)
      const blogs = await BlogModel.find({})
        .sort({ date: -1 })
        .skip(skip)
        .limit(limit);

      return NextResponse.json({
        blogs,
        pagination: {
          currentPage: page,
          totalPages: totalPages,
          totalBlogs: totalBlogs,
          hasNextPage: page < totalPages,
          hasPrevPage: page > 1,
          limit: limit
        }
      });
    }
  } catch (error) {
    console.error("Error in GET /api/blog:", error.message);
    return NextResponse.json(
      {
        success: false,
        message: "Database connection error. Please try again later.",
        blogs: [],
        pagination: {
          currentPage: 1,
          totalPages: 0,
          totalBlogs: 0,
          hasNextPage: false,
          hasPrevPage: false,
          limit: 10
        }
      },
      { status: 503 }
    );
  }
}


// API Endpoint For Uploading Blogs
export async function POST(request) {

  const formData = await request.formData();
  const timestamp = Date.now();

  const image = formData.get('image');
  const imageByteData = await image.arrayBuffer();
  const buffer = Buffer.from(imageByteData);
  const path = `./public/${timestamp}_${image.name}`;
  await writeFile(path, buffer);
  const imgUrl = `/${timestamp}_${image.name}`;

  const blogData = {
    title: `${formData.get('title')}`,
    description: `${formData.get('description')}`,
    category: `${formData.get('category')}`,
    author: `${formData.get('author')}`,
    image: `${imgUrl}`,
    authorImg: `${formData.get('authorImg')}`,
    commentsEnabled: formData.get('commentsEnabled') === 'true'
  }

  const newBlog = await BlogModel.create(blogData);
  console.log("Blog Saved");

  // Transfer temporary images to the new blog
  const tempBlogId = formData.get('tempBlogId');
  if (tempBlogId) {
    try {
      await ImageModel.updateMany(
        { blogId: tempBlogId },
        { blogId: newBlog._id.toString() }
      );
      console.log(`Transferred images from ${tempBlogId} to ${newBlog._id}`);
    } catch (error) {
      console.error("Error transferring images:", error);
    }
  }

  return NextResponse.json({ success: true, msg: "Blog Added" })
}

// Creating API Endpoint to delete Blog
export async function DELETE(request) {
  try {
    const id = await request.nextUrl.searchParams.get('id');
    const blog = await BlogModel.findById(id);

    if (!blog) {
      return NextResponse.json({ success: false, msg: "Blog not found" }, { status: 404 });
    }

    // Delete the image file if it exists
    if (blog.image) {
      fs.unlink(`./public${blog.image}`, () => { });
    }

    await BlogModel.findByIdAndDelete(id);
    return NextResponse.json({ success: true, msg: "Blog Deleted" });
  } catch (error) {
    console.error("Error in DELETE /api/blog:", error.message);
    return NextResponse.json(
      { success: false, msg: "Database connection error. Please try again later." },
      { status: 503 }
    );
  }
}

// API Endpoint For Updating Blogs
export async function PUT(request) {
  try {
    const formData = await request.formData();
    const blogId = formData.get('id');
    
    // Find the blog to update
    const blog = await BlogModel.findById(blogId);
    if (!blog) {
      return NextResponse.json({ success: false, msg: "Blog not found" }, { status: 404 });
    }
    
    // Update blog data
    blog.title = formData.get('title');
    blog.description = formData.get('description');
    blog.category = formData.get('category');
    blog.author = formData.get('author');
    blog.authorImg = formData.get('authorImg');
    blog.commentsEnabled = formData.get('commentsEnabled') === 'true';
    
    // Check if a new image was uploaded
    const image = formData.get('image');
    if (image && image.name) {
      // Process new image
      const imageByteData = await image.arrayBuffer();
      const buffer = Buffer.from(imageByteData);
      const timestamp = Date.now();
      const path = `./public/${timestamp}_${image.name}`;
      await writeFile(path, buffer);
      const imgUrl = `/${timestamp}_${image.name}`;
      
      // Delete old image if it exists
      if (blog.image) {
        try {
          fs.unlink(`./public${blog.image}`, () => {});
        } catch (error) {
          console.error("Error deleting old image:", error);
        }
      }
      
      // Update image URL
      blog.image = imgUrl;
    }
    
    // Save updated blog
    await blog.save();
    
    return NextResponse.json({ success: true, msg: "Blog Updated Successfully" });
  } catch (error) {
    console.error("Error updating blog:", error);
    return NextResponse.json({ success: false, msg: "Error updating blog" }, { status: 500 });
  }
}
