import { assets } from '@/Assets/assets'
import Image from 'next/image'
import Link from 'next/link'
import React from 'react'

const Footer = () => {
  return (
    <div className='flex justify-around flex-col gap-2 sm:gap-0 sm:flex-row bg-black py-5 items-center'>
      <Image src={assets.logo_light} alt='Mr.Blogger' width={120} />
      <div className='text-center'>
        <div className='flex justify-center gap-4 mb-2 flex-wrap'>
          <Link href='/about' className='text-sm text-white hover:underline'>
            About Us
          </Link>
          <Link href='/contact' className='text-sm text-white hover:underline'>
            Contact Us
          </Link>
          <Link href='/privacy-policy' className='text-sm text-white hover:underline'>
            Privacy Policy
          </Link>
          <Link href='/terms-and-conditions' className='text-sm text-white hover:underline'>
            Terms & Conditions
          </Link>
          <Link href='/disclaimer' className='text-sm text-white hover:underline'>
            Disclaimer
          </Link>
        </div>
        <p className='text-sm text-white'>All right reserved. Copyright @Mr.Blogger</p>
      </div>
      <div className='flex'>
        <Image src={assets.facebook_icon} alt='' width={40} />
        <Image src={assets.twitter_icon} alt='' width={40} />
        <Image src={assets.googleplus_icon} alt='' width={40} />
      </div>
    </div>
  )
}

export default Footer
