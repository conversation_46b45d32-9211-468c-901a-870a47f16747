const mongoose = require('mongoose');

const testConnection = async () => {
    try {
        const connectionString = process.env.MONGODB_URI || 'mongodb+srv://subhashanas:<EMAIL>/blog-app';
        
        console.log('Testing connection to:', connectionString.replace(/:[^:@]*@/, ':****@'));
        
        await mongoose.connect(connectionString, {
            serverSelectionTimeoutMS: 10000,
        });
        
        console.log('✅ Database connection successful!');
        
        // Test a simple query
        const collections = await mongoose.connection.db.listCollections().toArray();
        console.log('📁 Available collections:', collections.map(c => c.name));
        
        process.exit(0);
    } catch (error) {
        console.error('❌ Database connection failed:', error.message);
        process.exit(1);
    }
};

testConnection();
