'use client'
import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { toast } from 'react-toastify';
import CommentForm from './CommentForm';
import CommentItem from './CommentItem';

const CommentSection = ({ blogId, isLoggedIn, onLoginRequired }) => {
  const [comments, setComments] = useState([]);
  const [loading, setLoading] = useState(false);
  const [commentsEnabled, setCommentsEnabled] = useState(true);
  const [showComments, setShowComments] = useState(false);
  const [commentsLoaded, setCommentsLoaded] = useState(false);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    pages: 0
  });

  // Fetch comments
  const fetchComments = async (page = 1) => {
    try {
      setLoading(true);
      const response = await axios.get('/api/comments', {
        params: {
          blogId,
          page,
          limit: pagination.limit
        }
      });

      if (response.data.success) {
        setComments(response.data.comments);
        setPagination(response.data.pagination);
        setCommentsEnabled(true);
        setCommentsLoaded(true);
      } else {
        if (response.data.message.includes('disabled')) {
          setCommentsEnabled(false);
        }
        console.error('Failed to fetch comments:', response.data.message);
      }
    } catch (error) {
      console.error('Error fetching comments:', error);
      if (error.response?.data?.message?.includes('disabled')) {
        setCommentsEnabled(false);
      }
    } finally {
      setLoading(false);
    }
  };

  // Toggle comments visibility
  const toggleComments = () => {
    setShowComments(!showComments);
    // Only fetch comments when showing them for the first time
    if (!showComments && !commentsLoaded) {
      fetchComments();
    }
  };

  // Handle new comment submission
  const handleCommentSubmit = async (content) => {
    if (!isLoggedIn) {
      onLoginRequired();
      return;
    }

    try {
      const authToken = localStorage.getItem('authToken');
      const response = await axios.post('/api/comments', {
        blogId,
        content
      }, {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });

      if (response.data.success) {
        toast.success('Comment posted successfully!');
        // Add new comment to the beginning of the list
        setComments(prev => [response.data.comment, ...prev]);
        setPagination(prev => ({
          ...prev,
          total: prev.total + 1
        }));
      } else {
        toast.error(response.data.message || 'Failed to post comment');
      }
    } catch (error) {
      console.error('Error posting comment:', error);
      if (error.response?.status === 401) {
        toast.error('Please log in to comment');
        onLoginRequired();
      } else {
        toast.error(error.response?.data?.message || 'Failed to post comment');
      }
    }
  };

  // Handle reply submission
  const handleReplySubmit = async (parentCommentId, content) => {
    if (!isLoggedIn) {
      onLoginRequired();
      return;
    }

    try {
      const authToken = localStorage.getItem('authToken');
      const response = await axios.post('/api/comments', {
        blogId,
        content,
        parentCommentId
      }, {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });

      if (response.data.success) {
        toast.success('Reply posted successfully!');
        // Add reply to the parent comment
        setComments(prev => prev.map(comment => {
          if (comment._id === parentCommentId) {
            return {
              ...comment,
              replies: [...comment.replies, response.data.comment]
            };
          }
          return comment;
        }));
      } else {
        toast.error(response.data.message || 'Failed to post reply');
      }
    } catch (error) {
      console.error('Error posting reply:', error);
      if (error.response?.status === 401) {
        toast.error('Please log in to reply');
        onLoginRequired();
      } else {
        toast.error(error.response?.data?.message || 'Failed to post reply');
      }
    }
  };

  // Handle comment reaction
  const handleReaction = async (commentId, reactionType) => {
    if (!isLoggedIn) {
      onLoginRequired();
      return;
    }

    try {
      const authToken = localStorage.getItem('authToken');
      const response = await axios.post(`/api/comments/${commentId}/react`, {
        reactionType
      }, {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      });

      if (response.data.success) {
        // Update reaction counts in the comments
        const updateCommentReactions = (comments) => {
          return comments.map(comment => {
            if (comment._id === commentId) {
              return {
                ...comment,
                likeCount: response.data.likeCount,
                dislikeCount: response.data.dislikeCount,
                userReaction: response.data.reaction
              };
            }
            // Also check replies
            if (comment.replies && comment.replies.length > 0) {
              return {
                ...comment,
                replies: updateCommentReactions(comment.replies)
              };
            }
            return comment;
          });
        };

        setComments(prev => updateCommentReactions(prev));
      } else {
        toast.error(response.data.message || 'Failed to update reaction');
      }
    } catch (error) {
      console.error('Error updating reaction:', error);
      if (error.response?.status === 401) {
        toast.error('Please log in to react');
        onLoginRequired();
      } else {
        toast.error(error.response?.data?.message || 'Failed to update reaction');
      }
    }
  };

  // Load more comments
  const loadMoreComments = () => {
    if (pagination.page < pagination.pages) {
      fetchComments(pagination.page + 1);
    }
  };

  // Check if comments are enabled on component mount
  useEffect(() => {
    const checkCommentsEnabled = async () => {
      try {
        const response = await axios.get('/api/comments', {
          params: {
            blogId,
            page: 1,
            limit: 1
          }
        });
        setCommentsEnabled(response.data.success);
      } catch (error) {
        if (error.response?.data?.message?.includes('disabled')) {
          setCommentsEnabled(false);
        }
      }
    };

    if (blogId) {
      checkCommentsEnabled();
    }
  }, [blogId]);



  return (
    <div className="max-w-4xl mx-auto mt-12 p-6">
      {/* Comments Toggle Button */}
      <div className="flex items-center justify-between mb-6">
        <button
          onClick={commentsEnabled ? toggleComments : undefined}
          disabled={!commentsEnabled}
          className={`flex items-center gap-2 px-4 py-2 rounded-lg transition-colors ${
            commentsEnabled
              ? 'bg-black text-white hover:bg-gray-800'
              : 'bg-gray-300 text-gray-500 cursor-not-allowed'
          }`}
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
          </svg>
          <span>
            {!commentsEnabled
              ? 'Comments are turned off'
              : showComments
                ? 'Hide Comments'
                : 'Show Comments'
            }
            {commentsLoaded && commentsEnabled && ` (${pagination.total})`}
          </span>
          {commentsEnabled && (
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className={`transition-transform ${showComments ? 'rotate-180' : ''}`}
            >
              <polyline points="6,9 12,15 18,9"/>
            </svg>
          )}
        </button>

        {commentsLoaded && commentsEnabled && (
          <span className="text-gray-600 text-sm">
            {pagination.total} comment{pagination.total !== 1 ? 's' : ''}
          </span>
        )}
      </div>

      {/* Comments Section - Only show when toggled and comments are enabled */}
      {showComments && commentsEnabled && (
        <>
          {/* Comment Form */}
          <div className="mb-8">
            <CommentForm
              onSubmit={handleCommentSubmit}
              isLoggedIn={isLoggedIn}
              onLoginRequired={onLoginRequired}
              placeholder="Share your thoughts about this blog post..."
            />
          </div>

          {/* Comments List */}
          {loading ? (
            <div className="text-center py-8">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
              <p className="mt-2 text-gray-600">Loading comments...</p>
            </div>
          ) : comments.length === 0 ? (
            <div className="text-center py-8 bg-gray-50 rounded-lg">
              <p className="text-gray-600">No comments yet. Be the first to share your thoughts!</p>
            </div>
          ) : (
            <>
              <div className="space-y-6">
                {comments.map((comment) => (
                  <CommentItem
                    key={comment._id}
                    comment={comment}
                    onReply={handleReplySubmit}
                    onReaction={handleReaction}
                    isLoggedIn={isLoggedIn}
                    onLoginRequired={onLoginRequired}
                  />
                ))}
              </div>

              {/* Load More Button */}
              {pagination.page < pagination.pages && (
                <div className="text-center mt-8">
                  <button
                    onClick={loadMoreComments}
                    className="px-6 py-2 bg-black text-white border border-black hover:bg-gray-800 transition-colors"
                  >
                    Load More Comments
                  </button>
                </div>
              )}
            </>
          )}
        </>
      )}

      {/* Show message when comments are disabled and user tries to view them */}
      {!commentsEnabled && showComments && (
        <div className="mt-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
          <div className="flex items-center gap-2 text-gray-600">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <circle cx="12" cy="12" r="10"/>
              <line x1="15" y1="9" x2="9" y2="15"/>
              <line x1="9" y1="9" x2="15" y2="15"/>
            </svg>
            <p className="font-medium">Comments are currently disabled for this blog post.</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default CommentSection;
