"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/analytics/route";
exports.ids = ["app/api/analytics/route"];
exports.modules = {

/***/ "mongoose":
/*!***************************!*\
  !*** external "mongoose" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("mongoose");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fanalytics%2Froute&page=%2Fapi%2Fanalytics%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalytics%2Froute.js&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fanalytics%2Froute&page=%2Fapi%2Fanalytics%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalytics%2Froute.js&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_DSYS_Desktop_Mr_Blog_blog_app_app_api_analytics_route_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/analytics/route.js */ \"(rsc)/./app/api/analytics/route.js\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/analytics/route\",\n        pathname: \"/api/analytics\",\n        filename: \"route\",\n        bundlePath: \"app/api/analytics/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Mr.Blog\\\\blog-app\\\\app\\\\api\\\\analytics\\\\route.js\",\n    nextConfigOutput,\n    userland: C_Users_DSYS_Desktop_Mr_Blog_blog_app_app_api_analytics_route_js__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/analytics/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fanalytics%2Froute&page=%2Fapi%2Fanalytics%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalytics%2Froute.js&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/analytics/route.js":
/*!************************************!*\
  !*** ./app/api/analytics/route.js ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_config_db__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/config/db */ \"(rsc)/./lib/config/db.js\");\n/* harmony import */ var _lib_models_AnalyticsModel__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/models/AnalyticsModel */ \"(rsc)/./lib/models/AnalyticsModel.js\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _lib_utils_token__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils/token */ \"(rsc)/./lib/utils/token.js\");\n\n\n\n\n\n// Helper function to get user from token\nconst getUserFromToken = (request)=>{\n    try {\n        const authHeader = request.headers.get(\"authorization\");\n        if (!authHeader || !authHeader.startsWith(\"Bearer \")) {\n            console.log(\"Missing or invalid Authorization header\");\n            return null;\n        }\n        const token = authHeader.split(\" \")[1];\n        if (!token) {\n            console.log(\"No token found in Authorization header\");\n            return null;\n        }\n        console.log(\"Attempting to verify token...\");\n        // Use the verifyToken function from your token utility\n        const userData = (0,_lib_utils_token__WEBPACK_IMPORTED_MODULE_4__.verifyToken)(token);\n        console.log(\"Token verification result:\", userData);\n        return userData;\n    } catch (error) {\n        console.error(\"Error getting user from token:\", error);\n        return null;\n    }\n};\n// Track page view\nasync function POST(request) {\n    try {\n        await (0,_lib_config_db__WEBPACK_IMPORTED_MODULE_1__.ConnectDB)();\n        const body = await request.json();\n        const { path, contentType, blogId, referrer } = body;\n        // Get user info if logged in\n        const userData = getUserFromToken(request);\n        const userId = userData?.userId || null;\n        // Get IP address and hash it for privacy\n        const ip = request.headers.get(\"x-forwarded-for\") || request.headers.get(\"x-real-ip\") || \"127.0.0.1\";\n        const ipHash = crypto__WEBPACK_IMPORTED_MODULE_3___default().createHash(\"sha256\").update(ip + (process.env.IP_SALT || \"salt\")).digest(\"hex\");\n        // Get user agent\n        const userAgent = request.headers.get(\"user-agent\") || \"\";\n        // Create analytics entry\n        await _lib_models_AnalyticsModel__WEBPACK_IMPORTED_MODULE_2__[\"default\"].create({\n            path,\n            contentType,\n            blogId,\n            userId,\n            ipHash,\n            userAgent,\n            referrer,\n            timestamp: new Date()\n        });\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: true\n        });\n    } catch (error) {\n        console.error(\"Error tracking analytics:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: false,\n            message: \"Failed to track analytics\"\n        }, {\n            status: 500\n        });\n    }\n}\n// Get analytics data\nasync function GET(request) {\n    try {\n        await (0,_lib_config_db__WEBPACK_IMPORTED_MODULE_1__.ConnectDB)();\n        // Verify admin access\n        const userData = getUserFromToken(request);\n        console.log(\"User data from token:\", userData);\n        if (!userData || userData.role !== \"admin\") {\n            console.log(\"Unauthorized access attempt to analytics\");\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                success: false,\n                message: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const period = searchParams.get(\"period\") || \"7days\"; // Default to 7 days\n        const blogId = searchParams.get(\"blogId\"); // Optional blog ID filter\n        console.log(\"Fetching analytics for period:\", period);\n        // Calculate date range based on period\n        const endDate = new Date();\n        let startDate = new Date();\n        switch(period){\n            case \"24hours\":\n                startDate.setHours(startDate.getHours() - 24);\n                break;\n            case \"7days\":\n                startDate.setDate(startDate.getDate() - 7);\n                break;\n            case \"30days\":\n                startDate.setDate(startDate.getDate() - 30);\n                break;\n            case \"90days\":\n                startDate.setDate(startDate.getDate() - 90);\n                break;\n            default:\n                startDate.setDate(startDate.getDate() - 7);\n        }\n        // Base query with date range\n        const query = {\n            timestamp: {\n                $gte: startDate,\n                $lte: endDate\n            }\n        };\n        // Add blog ID filter if provided\n        if (blogId) {\n            query.blogId = blogId;\n        }\n        console.log(\"Analytics query:\", JSON.stringify(query));\n        // Get total page views\n        const totalViews = await _lib_models_AnalyticsModel__WEBPACK_IMPORTED_MODULE_2__[\"default\"].countDocuments(query);\n        console.log(\"Total views:\", totalViews);\n        // Get unique visitors (by IP hash)\n        const uniqueVisitors = await _lib_models_AnalyticsModel__WEBPACK_IMPORTED_MODULE_2__[\"default\"].distinct(\"ipHash\", query).then((ips)=>ips.length);\n        console.log(\"Unique visitors:\", uniqueVisitors);\n        // Get views by content type\n        const viewsByType = await _lib_models_AnalyticsModel__WEBPACK_IMPORTED_MODULE_2__[\"default\"].aggregate([\n            {\n                $match: query\n            },\n            {\n                $group: {\n                    _id: \"$contentType\",\n                    count: {\n                        $sum: 1\n                    }\n                }\n            }\n        ]);\n        console.log(\"Views by type:\", viewsByType);\n        // Get top pages/posts\n        const topPages = await _lib_models_AnalyticsModel__WEBPACK_IMPORTED_MODULE_2__[\"default\"].aggregate([\n            {\n                $match: query\n            },\n            {\n                $group: {\n                    _id: \"$path\",\n                    count: {\n                        $sum: 1\n                    }\n                }\n            },\n            {\n                $sort: {\n                    count: -1\n                }\n            },\n            {\n                $limit: 10\n            }\n        ]);\n        console.log(\"Top pages:\", topPages);\n        // Get traffic over time (daily)\n        const trafficOverTime = await _lib_models_AnalyticsModel__WEBPACK_IMPORTED_MODULE_2__[\"default\"].aggregate([\n            {\n                $match: query\n            },\n            {\n                $group: {\n                    _id: {\n                        $dateToString: {\n                            format: \"%Y-%m-%d\",\n                            date: \"$timestamp\"\n                        }\n                    },\n                    count: {\n                        $sum: 1\n                    }\n                }\n            },\n            {\n                $sort: {\n                    \"_id\": 1\n                }\n            }\n        ]);\n        console.log(\"Traffic over time:\", trafficOverTime);\n        // Get referrers\n        const topReferrers = await _lib_models_AnalyticsModel__WEBPACK_IMPORTED_MODULE_2__[\"default\"].aggregate([\n            {\n                $match: {\n                    ...query,\n                    referrer: {\n                        $ne: null,\n                        $ne: \"\"\n                    }\n                }\n            },\n            {\n                $group: {\n                    _id: \"$referrer\",\n                    count: {\n                        $sum: 1\n                    }\n                }\n            },\n            {\n                $sort: {\n                    count: -1\n                }\n            },\n            {\n                $limit: 10\n            }\n        ]);\n        console.log(\"Top referrers:\", topReferrers);\n        const responseData = {\n            success: true,\n            data: {\n                totalViews,\n                uniqueVisitors,\n                viewsByType,\n                topPages,\n                trafficOverTime,\n                topReferrers\n            }\n        };\n        console.log(\"Analytics response data:\", JSON.stringify(responseData));\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(responseData);\n    } catch (error) {\n        console.error(\"Error fetching analytics:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            success: false,\n            message: \"Failed to fetch analytics\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/analytics/route.js\n");

/***/ }),

/***/ "(rsc)/./lib/config/db.js":
/*!**************************!*\
  !*** ./lib/config/db.js ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConnectDB: () => (/* binding */ ConnectDB)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst ConnectDB = async ()=>{\n    try {\n        // Check if already connected\n        if ((mongoose__WEBPACK_IMPORTED_MODULE_0___default().connections)[0].readyState) {\n            console.log(\"DB Already Connected\");\n            return;\n        }\n        // Try environment variable first, then fallback to Atlas, then local MongoDB\n        const connectionString = process.env.MONGODB_URI || \"mongodb+srv://subhashanas:<EMAIL>/blog-app\" || 0;\n        await mongoose__WEBPACK_IMPORTED_MODULE_0___default().connect(connectionString, {\n            serverSelectionTimeoutMS: 5000,\n            socketTimeoutMS: 45000\n        });\n        console.log(\"DB Connected\");\n    } catch (error) {\n        console.error(\"DB Connection Error:\", error.message);\n    // Don't throw the error to prevent 500 responses\n    // The API will handle the case where DB is not connected\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./lib/config/db.js\n");

/***/ }),

/***/ "(rsc)/./lib/models/AnalyticsModel.js":
/*!**************************************!*\
  !*** ./lib/models/AnalyticsModel.js ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mongoose */ \"mongoose\");\n/* harmony import */ var mongoose__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mongoose__WEBPACK_IMPORTED_MODULE_0__);\n\nconst AnalyticsSchema = new (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema)({\n    // Page or post being viewed\n    path: {\n        type: String,\n        required: true,\n        index: true\n    },\n    // Type of content (blog, page, etc.)\n    contentType: {\n        type: String,\n        required: true,\n        enum: [\n            \"blog\",\n            \"page\",\n            \"home\",\n            \"other\"\n        ],\n        default: \"other\"\n    },\n    // Associated blog ID if applicable\n    blogId: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: \"blogs\",\n        index: true\n    },\n    // User ID if logged in\n    userId: {\n        type: (mongoose__WEBPACK_IMPORTED_MODULE_0___default().Schema).Types.ObjectId,\n        ref: \"users\",\n        index: true\n    },\n    // IP address (hashed for privacy)\n    ipHash: {\n        type: String,\n        index: true\n    },\n    // User agent info\n    userAgent: String,\n    // Referrer URL\n    referrer: String,\n    // Timestamp\n    timestamp: {\n        type: Date,\n        default: Date.now,\n        index: true\n    }\n});\n// Create compound indexes for efficient querying\nAnalyticsSchema.index({\n    path: 1,\n    timestamp: 1\n});\nAnalyticsSchema.index({\n    contentType: 1,\n    timestamp: 1\n});\nAnalyticsSchema.index({\n    blogId: 1,\n    timestamp: 1\n});\n// Check if model exists before creating\nconst AnalyticsModel = (mongoose__WEBPACK_IMPORTED_MODULE_0___default().models).analytics || mongoose__WEBPACK_IMPORTED_MODULE_0___default().model(\"analytics\", AnalyticsSchema);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (AnalyticsModel);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/models/AnalyticsModel.js\n");

/***/ }),

/***/ "(rsc)/./lib/utils/token.js":
/*!****************************!*\
  !*** ./lib/utils/token.js ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createToken: () => (/* binding */ createToken),\n/* harmony export */   verifyToken: () => (/* binding */ verifyToken)\n/* harmony export */ });\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n// Simple token utilities without external dependencies\n\n// Secret key for JWT signing - in production, use environment variables\nconst JWT_SECRET = process.env.JWT_SECRET || \"your-secret-key-here\";\n// Create a token using JWT\nconst createToken = (payload)=>{\n    return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign(payload, JWT_SECRET, {\n        expiresIn: \"7d\"\n    });\n};\n// Verify a token using JWT\nconst verifyToken = (token)=>{\n    try {\n        return jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, JWT_SECRET);\n    } catch (error) {\n        console.error(\"Token verification error:\", error.message);\n        return null;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvdXRpbHMvdG9rZW4uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBLHVEQUF1RDtBQUN4QjtBQUUvQix3RUFBd0U7QUFDeEUsTUFBTUMsYUFBYUMsUUFBUUMsR0FBRyxDQUFDRixVQUFVLElBQUk7QUFFN0MsMkJBQTJCO0FBQ3BCLE1BQU1HLGNBQWMsQ0FBQ0M7SUFDMUIsT0FBT0wsd0RBQVEsQ0FBQ0ssU0FBU0osWUFBWTtRQUFFTSxXQUFXO0lBQUs7QUFDekQsRUFBRTtBQUVGLDJCQUEyQjtBQUNwQixNQUFNQyxjQUFjLENBQUNDO0lBQzFCLElBQUk7UUFDRixPQUFPVCwwREFBVSxDQUFDUyxPQUFPUjtJQUMzQixFQUFFLE9BQU9VLE9BQU87UUFDZEMsUUFBUUQsS0FBSyxDQUFDLDZCQUE2QkEsTUFBTUUsT0FBTztRQUN4RCxPQUFPO0lBQ1Q7QUFDRixFQUFFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbmV4dC1ibG9nLWFwcC8uL2xpYi91dGlscy90b2tlbi5qcz9iOTgxIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIFNpbXBsZSB0b2tlbiB1dGlsaXRpZXMgd2l0aG91dCBleHRlcm5hbCBkZXBlbmRlbmNpZXNcbmltcG9ydCBqd3QgZnJvbSAnanNvbndlYnRva2VuJztcblxuLy8gU2VjcmV0IGtleSBmb3IgSldUIHNpZ25pbmcgLSBpbiBwcm9kdWN0aW9uLCB1c2UgZW52aXJvbm1lbnQgdmFyaWFibGVzXG5jb25zdCBKV1RfU0VDUkVUID0gcHJvY2Vzcy5lbnYuSldUX1NFQ1JFVCB8fCAneW91ci1zZWNyZXQta2V5LWhlcmUnO1xuXG4vLyBDcmVhdGUgYSB0b2tlbiB1c2luZyBKV1RcbmV4cG9ydCBjb25zdCBjcmVhdGVUb2tlbiA9IChwYXlsb2FkKSA9PiB7XG4gIHJldHVybiBqd3Quc2lnbihwYXlsb2FkLCBKV1RfU0VDUkVULCB7IGV4cGlyZXNJbjogJzdkJyB9KTtcbn07XG5cbi8vIFZlcmlmeSBhIHRva2VuIHVzaW5nIEpXVFxuZXhwb3J0IGNvbnN0IHZlcmlmeVRva2VuID0gKHRva2VuKSA9PiB7XG4gIHRyeSB7XG4gICAgcmV0dXJuIGp3dC52ZXJpZnkodG9rZW4sIEpXVF9TRUNSRVQpO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoXCJUb2tlbiB2ZXJpZmljYXRpb24gZXJyb3I6XCIsIGVycm9yLm1lc3NhZ2UpO1xuICAgIHJldHVybiBudWxsO1xuICB9XG59O1xuXG4iXSwibmFtZXMiOlsiand0IiwiSldUX1NFQ1JFVCIsInByb2Nlc3MiLCJlbnYiLCJjcmVhdGVUb2tlbiIsInBheWxvYWQiLCJzaWduIiwiZXhwaXJlc0luIiwidmVyaWZ5VG9rZW4iLCJ0b2tlbiIsInZlcmlmeSIsImVycm9yIiwiY29uc29sZSIsIm1lc3NhZ2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/utils/token.js\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/ms","vendor-chunks/semver","vendor-chunks/jsonwebtoken","vendor-chunks/jws","vendor-chunks/ecdsa-sig-formatter","vendor-chunks/safe-buffer","vendor-chunks/lodash.once","vendor-chunks/lodash.isstring","vendor-chunks/lodash.isplainobject","vendor-chunks/lodash.isnumber","vendor-chunks/lodash.isinteger","vendor-chunks/lodash.isboolean","vendor-chunks/lodash.includes","vendor-chunks/jwa","vendor-chunks/buffer-equal-constant-time"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fanalytics%2Froute&page=%2Fapi%2Fanalytics%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalytics%2Froute.js&appDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSYS%5CDesktop%5CMr.Blog%5Cblog-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();