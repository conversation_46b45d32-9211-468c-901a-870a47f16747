import { NextResponse } from 'next/server';
import { ConnectDB } from '@/lib/config/db';
import AnalyticsModel from '@/lib/models/AnalyticsModel';
import { verifyToken } from '@/lib/utils/token';

// Helper function to get user from token
const getUserFromToken = (request) => {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      console.log('Missing or invalid Authorization header');
      return null;
    }

    const token = authHeader.split(' ')[1];
    if (!token) {
      console.log('No token found in Authorization header');
      return null;
    }

    console.log('Attempting to verify token...');
    const userData = verifyToken(token);
    console.log('Token verification result:', userData);
    return userData;
  } catch (error) {
    console.error('Error getting user from token:', error);
    return null;
  }
};

export async function GET(request) {
  try {
    await ConnectDB();
    
    // Verify authentication
    const userData = getUserFromToken(request);
    if (!userData) {
      return NextResponse.json({ success: false, message: "Authentication required" }, { status: 401 });
    }
    
    const { searchParams } = new URL(request.url);
    const blogId = searchParams.get('id');
    
    if (!blogId) {
      return NextResponse.json({ success: false, message: "Blog ID is required" }, { status: 400 });
    }
    
    // Get total views for this blog
    const totalViews = await AnalyticsModel.countDocuments({ blogId });
    
    // Get unique visitors for this blog
    const uniqueVisitors = await AnalyticsModel.distinct('ipHash', { blogId }).then(ips => ips.length);
    
    // Get views over time (daily)
    const viewsOverTime = await AnalyticsModel.aggregate([
      { $match: { blogId: blogId } },
      {
        $group: {
          _id: {
            $dateToString: { format: '%Y-%m-%d', date: '$timestamp' }
          },
          count: { $sum: 1 }
        }
      },
      { $sort: { '_id': 1 } }
    ]);
    
    return NextResponse.json({
      success: true,
      analytics: {
        totalViews,
        uniqueVisitors,
        viewsOverTime
      }
    });
  } catch (error) {
    console.error("Error fetching blog analytics:", error);
    return NextResponse.json({ success: false, message: "Failed to fetch blog analytics" }, { status: 500 });
  }
}

