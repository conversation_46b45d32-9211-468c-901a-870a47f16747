'use client'
import axios from 'axios'
import React, { useState, useEffect, useCallback } from 'react'
import { toast } from 'react-toastify'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import Cropper from 'react-easy-crop'

const SettingsPage = () => {
  const router = useRouter();
  const [categories, setCategories] = useState([]);
  const [authors, setAuthors] = useState([]);
  const [newCategory, setNewCategory] = useState('');
  const [newAuthor, setNewAuthor] = useState({
    name: '',
    image: null,
    bio: ''
  });
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('categories'); // 'categories' or 'authors'
  const [authorImagePreview, setAuthorImagePreview] = useState(null);
  
  // Cropper state
  const [showCropper, setShowCropper] = useState(false);
  const [cropImage, setCropImage] = useState(null);
  const [crop, setCrop] = useState({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(1);
  const [croppedAreaPixels, setCroppedAreaPixels] = useState(null);
  const [croppedImage, setCroppedImage] = useState(null);

  // Fetch categories and authors on component mount
  useEffect(() => {
    fetchCategories();
    fetchAuthors();
  }, []);

  const fetchCategories = async () => {
    try {
      const response = await axios.get('/api/categories');
      if (response.data.success) {
        setCategories(response.data.categories);
      }
    } catch (error) {
      console.error("Error fetching categories:", error);
      toast.error("Failed to load categories");
    }
  };

  const fetchAuthors = async () => {
    try {
      const response = await axios.get('/api/authors');
      if (response.data.success) {
        setAuthors(response.data.authors);
      }
    } catch (error) {
      console.error("Error fetching authors:", error);
      toast.error("Failed to load authors");
    }
  };

  const handleAddCategory = async (e) => {
    e.preventDefault();
    if (!newCategory.trim()) return;
    
    try {
      setLoading(true);
      const response = await axios.post('/api/categories', { name: newCategory });
      
      if (response.data.success) {
        toast.success("Category added successfully");
        setNewCategory('');
        
        // Update categories with the returned list
        if (response.data.categories) {
          setCategories(response.data.categories);
        } else {
          // If categories not returned, fetch them
          await fetchCategories();
        }
      } else {
        toast.error(response.data.message || "Failed to add category");
      }
    } catch (error) {
      console.error("Error adding category:", error);
      if (error.response && error.response.status === 409) {
        toast.error("Category already exists");
      } else {
        toast.error(error.response?.data?.message || "Failed to add category");
      }
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteCategory = async (id) => {
    try {
      const response = await axios.delete(`/api/categories?id=${id}`);
      if (response.data.success) {
        toast.success("Category deleted successfully");
        // Update the categories list
        setCategories(categories.filter(cat => cat._id !== id));
      } else {
        toast.error(response.data.message || "Failed to delete category");
      }
    } catch (error) {
      console.error("Error deleting category:", error);
      toast.error(error.response?.data?.message || "Failed to delete category");
    }
  };

  const handleAuthorImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = () => {
        setCropImage(reader.result);
        setShowCropper(true);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleAuthorChange = (e) => {
    const { name, value } = e.target;
    setNewAuthor({...newAuthor, [name]: value});
  };

  const onCropComplete = useCallback((croppedArea, croppedAreaPixels) => {
    setCroppedAreaPixels(croppedAreaPixels);
  }, []);

  const createImage = (url) =>
    new Promise((resolve, reject) => {
      const image = new Image();
      image.addEventListener('load', () => resolve(image));
      image.addEventListener('error', (error) => reject(error));
      image.src = url;
    });

  const getCroppedImg = async (imageSrc, pixelCrop) => {
    const image = await createImage(imageSrc);
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    const maxSize = Math.max(image.width, image.height);
    const safeArea = 2 * ((maxSize / 2) * Math.sqrt(2));

    canvas.width = safeArea;
    canvas.height = safeArea;

    ctx.drawImage(
      image,
      safeArea / 2 - image.width * 0.5,
      safeArea / 2 - image.height * 0.5
    );
    const data = ctx.getImageData(0, 0, safeArea, safeArea);

    canvas.width = pixelCrop.width;
    canvas.height = pixelCrop.height;

    ctx.putImageData(
      data,
      Math.round(0 - safeArea / 2 + image.width * 0.5 - pixelCrop.x),
      Math.round(0 - safeArea / 2 + image.height * 0.5 - pixelCrop.y)
    );

    return new Promise((resolve) => {
      canvas.toBlob((blob) => {
        resolve(blob);
      }, 'image/jpeg');
    });
  };

  const applyCrop = async () => {
    try {
      if (!croppedAreaPixels) return;
      
      const croppedBlob = await getCroppedImg(
        cropImage,
        croppedAreaPixels
      );
      
      const croppedImageUrl = URL.createObjectURL(croppedBlob);
      setCroppedImage(croppedBlob);
      setAuthorImagePreview(croppedImageUrl);
      setShowCropper(false);
    } catch (e) {
      console.error('Error applying crop:', e);
      toast.error('Failed to crop image');
    }
  };

  const cancelCrop = () => {
    setShowCropper(false);
    setCropImage(null);
  };

  const handleAddAuthor = async (e) => {
    e.preventDefault();
    if (!newAuthor.name.trim()) {
      toast.error("Author name is required");
      return;
    }
    
    try {
      setLoading(true);
      const formData = new FormData();
      formData.append('name', newAuthor.name);
      formData.append('bio', newAuthor.bio);
      
      if (croppedImage) {
        // Create a file from the blob
        const file = new File([croppedImage], "cropped_image.jpg", { type: "image/jpeg" });
        formData.append('image', file);
      }
      
      const response = await axios.post('/api/authors', formData);
      
      if (response.data.success) {
        toast.success("Author added successfully");
        setNewAuthor({
          name: '',
          image: null,
          bio: ''
        });
        setAuthorImagePreview(null);
        setCroppedImage(null);
        
        // Update authors list
        await fetchAuthors();
      } else {
        toast.error(response.data.message || "Failed to add author");
      }
    } catch (error) {
      console.error("Error adding author:", error);
      toast.error(error.response?.data?.message || "Failed to add author");
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteAuthor = async (id) => {
    try {
      const response = await axios.delete(`/api/authors?id=${id}`);
      if (response.data.success) {
        toast.success("Author deleted successfully");
        // Update the authors list
        setAuthors(authors.filter(author => author._id !== id));
      } else {
        toast.error(response.data.message || "Failed to delete author");
      }
    } catch (error) {
      console.error("Error deleting author:", error);
      toast.error(error.response?.data?.message || "Failed to delete author");
    }
  };

  return (
    <div className='flex-1 pt-5 px-5 sm:pt-12 sm:pl-16'>
      <h1 className='text-2xl font-bold mb-6'>Settings</h1>

      {/* Tabs */}
      <div className='flex border-b mb-6'>
        <button
          className={`py-2 px-4 ${activeTab === 'categories' ? 'border-b-2 border-black font-medium' : 'text-gray-500'}`}
          onClick={() => setActiveTab('categories')}
        >
          Categories
        </button>
        <button
          className={`py-2 px-4 ${activeTab === 'authors' ? 'border-b-2 border-black font-medium' : 'text-gray-500'}`}
          onClick={() => setActiveTab('authors')}
        >
          Authors
        </button>
      </div>

      {/* Categories Tab */}
      {activeTab === 'categories' && (
        <div>
          <div className='bg-white p-6 rounded-lg border border-gray-300 shadow-md mb-8'>
            <h2 className='text-xl font-semibold mb-4'>Manage Blog Categories</h2>
            
            {/* Add new category form */}
            <form onSubmit={handleAddCategory} className='mb-6'>
              <div className='flex gap-3'>
                <input
                  type='text'
                  value={newCategory}
                  onChange={(e) => setNewCategory(e.target.value)}
                  placeholder='Enter new category name'
                  className='flex-1 px-4 py-2 border border-gray-300 rounded-md'
                  required
                />
                <button
                  type='submit'
                  className='px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800'
                  disabled={loading}
                >
                  Add Category
                </button>
              </div>
            </form>
            
            {/* Categories list */}
            <div className='relative w-full overflow-x-auto border border-gray-400 rounded-md'>
              <table className='w-full text-sm text-gray-500 divide-y divide-gray-200'>
                <thead className='bg-gray-50'>
                  <tr>
                    <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                      Category Name
                    </th>
                    <th className='px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider'>
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className='bg-white divide-y divide-gray-200'>
                  {categories.map((category) => (
                    <tr key={category._id}>
                      <td className='px-6 py-4 whitespace-nowrap'>
                        <div className='text-sm font-medium text-gray-900'>{category.name}</div>
                      </td>
                      <td className='px-6 py-4 whitespace-nowrap text-right text-sm font-medium'>
                        <button
                          onClick={() => handleDeleteCategory(category._id)}
                          className='text-red-600 hover:text-red-900'
                        >
                          Delete
                        </button>
                      </td>
                    </tr>
                  ))}
                  {categories.length === 0 && (
                    <tr>
                      <td colSpan='2' className='px-6 py-4 text-center text-sm text-gray-500'>
                        No categories found
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}
      
      {/* Authors Tab */}
      {activeTab === 'authors' && (
        <div>
          <div className='bg-white p-6 rounded-lg border border-gray-300 shadow-md mb-8'>
            <h2 className='text-xl font-semibold mb-4'>Manage Blog Authors</h2>
            
            {showCropper ? (
              <div className='mb-6 max-w-2xl'>
                <h3 className='text-lg font-semibold mb-4'>Adjust Profile Image</h3>
                <div className='relative h-80 mb-4'>
                  <Cropper
                    image={cropImage}
                    crop={crop}
                    zoom={zoom}
                    aspect={1}
                    cropShape="round"
                    onCropChange={setCrop}
                    onCropComplete={onCropComplete}
                    onZoomChange={setZoom}
                  />
                </div>
                <div className='mb-4'>
                  <label className='block text-sm font-medium text-gray-700 mb-1'>
                    Zoom: {zoom.toFixed(1)}x
                  </label>
                  <input
                    type='range'
                    min={1}
                    max={3}
                    step={0.1}
                    value={zoom}
                    onChange={(e) => setZoom(parseFloat(e.target.value))}
                    className='w-full'
                  />
                </div>
                <div className='flex gap-3'>
                  <button
                    type='button'
                    onClick={applyCrop}
                    className='px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800'
                  >
                    Apply
                  </button>
                  <button
                    type='button'
                    onClick={cancelCrop}
                    className='px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100'
                  >
                    Cancel
                  </button>
                </div>
              </div>
            ) : (
              /* Add new author form */
              <form onSubmit={handleAddAuthor} className='mb-6'>
                <div className='mb-4'>
                  <label className='block text-gray-700 text-sm font-bold mb-2'>
                    Author Name
                  </label>
                  <input
                    type='text'
                    name='name'
                    value={newAuthor.name}
                    onChange={handleAuthorChange}
                    placeholder='Enter author name'
                    className='w-full px-4 py-2 border border-gray-300 rounded-md'
                    required
                  />
                </div>
                
                <div className='mb-4'>
                  <label className='block text-gray-700 text-sm font-bold mb-2'>
                    Author Bio
                  </label>
                  <textarea
                    name='bio'
                    value={newAuthor.bio}
                    onChange={handleAuthorChange}
                    placeholder='Enter author bio'
                    className='w-full px-4 py-2 border border-gray-300 rounded-md'
                    rows={3}
                  />
                </div>
                
                <div className='mb-4'>
                  <label className='block text-gray-700 text-sm font-bold mb-2'>
                    Author Image
                  </label>
                  <div className='flex items-center'>
                    {authorImagePreview && (
                      <div className='mr-4'>
                        <img 
                          src={authorImagePreview} 
                          alt="Author preview" 
                          className='w-16 h-16 object-cover rounded-full border-2 border-gray-200'
                        />
                      </div>
                    )}
                    <input
                      type='file'
                      accept='image/*'
                      onChange={handleAuthorImageChange}
                      className='w-full px-4 py-2 border border-gray-300 rounded-md'
                    />
                  </div>
                </div>
                
                <button
                  type='submit'
                  className='px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800'
                  disabled={loading}
                >
                  Add Author
                </button>
              </form>
            )}
            
            {/* Authors list */}
            <div className='relative w-full overflow-x-auto border border-gray-400 rounded-md'>
              <table className='w-full text-sm text-gray-500 divide-y divide-gray-200'>
                <thead className='bg-gray-50'>
                  <tr>
                    <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                      Author
                    </th>
                    <th className='px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider'>
                      Bio
                    </th>
                    <th className='px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider'>
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className='bg-white divide-y divide-gray-200'>
                  {authors.map((author) => (
                    <tr key={author._id}>
                      <td className='px-6 py-4 whitespace-nowrap'>
                        <div className='flex items-center'>
                          <div className='flex-shrink-0 h-10 w-10'>
                            <img 
                              className='h-10 w-10 rounded-full object-cover' 
                              src={author.image || '/author_img.png'} 
                              alt={author.name} 
                            />
                          </div>
                          <div className='ml-4'>
                            <div className='text-sm font-medium text-gray-900'>{author.name}</div>
                          </div>
                        </div>
                      </td>
                      <td className='px-6 py-4'>
                        <div className='text-sm text-gray-500 truncate max-w-xs'>{author.bio || 'No bio available'}</div>
                      </td>
                      <td className='px-6 py-4 whitespace-nowrap text-right text-sm font-medium'>
                        <div className='flex gap-3 justify-end'>
                          <Link 
                            href={`/admin/editAuthor/${author._id}`} 
                            className='cursor-pointer text-blue-600 hover:underline'
                          >
                            Edit
                          </Link>
                          <button
                            onClick={() => handleDeleteAuthor(author._id)}
                            className='cursor-pointer text-red-600 hover:underline'
                          >
                            Delete
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                  {authors.length === 0 && (
                    <tr>
                      <td colSpan='3' className='px-6 py-4 text-center text-sm text-gray-500'>
                        No authors found
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SettingsPage;






